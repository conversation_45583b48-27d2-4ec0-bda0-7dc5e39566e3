import { useEffect, useState } from "react";

type TabType = "essentiel" | "carte" | "presentation" | "horaires";

export const useProfileNavigation = (
  stickyRef: React.RefObject<HTMLDivElement>,
) => {
  const [activeTab, setActiveTab] = useState<TabType>("essentiel");

  // Afficher les sections disponibles au chargement
  useEffect(() => {
    // Fonction pour déterminer quelle section est actuellement visible
    const determineVisibleSection = () => {
      // Si la référence n'est pas disponible, ne rien faire
      if (!stickyRef.current) return;

      // Récupérer toutes les sections avec un ID
      const sections = Array.from(
        document.querySelectorAll<HTMLElement>("section[id]"),
      );

      if (sections.length === 0) return;

      // Hauteur de la navigation (pour le décalage)
      const navHeight = stickyRef.current.offsetHeight || 0;

      // Position de défilement actuelle + offset pour tenir compte de la navigation
      const scrollPosition = window.scrollY + navHeight + 50;

      // Calculer la visibilité de chaque section
      const visibleSections = sections.map((section) => {
        const rect = section.getBoundingClientRect();
        const sectionTop = rect.top + window.scrollY;
        const sectionBottom = rect.bottom + window.scrollY;

        // Calculer quelle partie de la section est visible dans la fenêtre
        const visibleTop = Math.max(sectionTop, window.scrollY);
        const visibleBottom = Math.min(
          sectionBottom,
          window.scrollY + window.innerHeight,
        );
        const visibleHeight = Math.max(0, visibleBottom - visibleTop);

        // Pourcentage de la section qui est visible
        const visibilityPercentage = visibleHeight / rect.height;

        return {
          section,
          id: section.id,
          visibilityPercentage,
          // Distance entre le haut de la section et la position de défilement actuelle
          distanceFromTop: Math.abs(sectionTop - scrollPosition),
        };
      });

      // Trier les sections par visibilité (la plus visible en premier)
      // En cas d'égalité, prendre celle qui est la plus proche du haut de l'écran
      visibleSections.sort((a, b) => {
        if (Math.abs(a.visibilityPercentage - b.visibilityPercentage) < 0.1) {
          return a.distanceFromTop - b.distanceFromTop;
        }
        return b.visibilityPercentage - a.visibilityPercentage;
      });

      // Prendre la section la plus visible
      const mostVisibleSection = visibleSections[0];

      // Mettre à jour l'onglet actif si une section est trouvée
      if (mostVisibleSection && mostVisibleSection.visibilityPercentage > 0) {
        setActiveTab(mostVisibleSection.id as TabType);
      } else if (sections.length > 0) {
        // Si aucune section n'est visible, utiliser la première
        setActiveTab(sections[0].id as TabType);
      }
    };

    // Fonction throttle pour limiter le nombre d'appels
    let isThrottled = false;
    const throttledScrollHandler = () => {
      if (!isThrottled) {
        determineVisibleSection();
        isThrottled = true;
        setTimeout(() => {
          isThrottled = false;
        }, 100); // Limiter à une exécution toutes les 100ms
      }
    };

    // Appeler la fonction au chargement initial
    determineVisibleSection();

    // Ajouter un écouteur d'événement pour le défilement avec throttle
    window.addEventListener("scroll", throttledScrollHandler);

    // Nettoyer l'écouteur d'événement lors du démontage
    return () => {
      window.removeEventListener("scroll", throttledScrollHandler);
    };
  }, [stickyRef]);

  const handleTabClick = (tab: string, event: React.MouseEvent) => {
    event.preventDefault();
    const element = document.getElementById(tab);
    if (element) {
      const navHeight = stickyRef.current?.offsetHeight || 0;
      const elementPosition = element.getBoundingClientRect().top +
        window.scrollY;
      window.scrollTo({
        top: elementPosition - navHeight - 20,
        behavior: "smooth",
      });
    }
    setActiveTab(tab as TabType);
  };

  return {
    activeTab,
    handleTabClick,
  };
};
