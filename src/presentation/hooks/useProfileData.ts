import { useEffect, useState, useCallback } from "react";
import { ProfessionalCardDTO } from "@/domain/DTOS";
import useSearchProfessional from "./use-search-professional";
import { toast } from "sonner";

/**
 * Interface pour les données étendues du profil professionnel
 * Inclut les nouvelles informations du formulaire d'inscription
 */
export interface ExtendedProfessionalProfile extends ProfessionalCardDTO {
  // Informations professionnelles étendues
  diplomes?: Array<{
    id?: number;
    nom_diplome: string;
    etablissement: string;
    date_obtention: string;
    description?: string;
  }>;
  experiences?: Array<{
    id?: number;
    poste: string;
    etablissement: string;
    date_debut: string;
    date_fin?: string;
    est_actuel: boolean;
    description?: string;
  }>;
  publications?: Array<{
    id?: number;
    titre: string;
    auteurs: string;
    date_publication: string;
    lien?: string;
    description?: string;
  }>;
  langues?: Array<{
    id?: number;
    nom_langue: string;
    niveau: string;
  }>;
  motCles?: Array<{
    id?: number;
    mot_cle: string;
  }>;
  // Informations de contact publiques (filtrées)
  modes_paiement_acceptes?: string[];
  raison_sociale?: string;
  informations_acces?: string;
}

/**
 * Hook personnalisé pour la gestion des données du profil professionnel
 * Suit le pattern useDashboardData avec gestion d'erreurs et loading states
 * 
 * @param professionalId - ID du professionnel à récupérer
 * @returns Données du profil, états de chargement et fonctions utilitaires
 */
export const useProfileData = (professionalId: number | null) => {
  const { currentProfessional, searchProfessionalById, loading } = useSearchProfessional();
  const [profileData, setProfileData] = useState<ExtendedProfessionalProfile | null>(null);
  const [error, setError] = useState<string | null>(null);

  /**
   * Fonction pour filtrer les informations sensibles
   * Assure que seules les informations publiques sont affichées
   */
  const filterSensitiveData = useCallback((professional: ProfessionalCardDTO): ExtendedProfessionalProfile => {
    // Créer une copie des données en excluant les informations sensibles
    const {
      // Exclure les informations sensibles
      // email, telephone sont déjà exclus du DTO public
      ...publicData
    } = professional;

    return {
      ...publicData,
      // Ajouter des champs par défaut pour les nouvelles fonctionnalités
      diplomes: [],
      experiences: [],
      publications: [],
      langues: [],
      motCles: [],
      modes_paiement_acceptes: [],
    } as ExtendedProfessionalProfile;
  }, []);

  /**
   * Récupération des données du professionnel
   */
  const fetchProfileData = useCallback(async (id: number) => {
    try {
      setError(null);
      await searchProfessionalById({ id });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur lors de la récupération du profil";
      setError(errorMessage);
      toast.error(errorMessage);
    }
  }, [searchProfessionalById]);

  /**
   * Effet pour charger les données initiales
   */
  useEffect(() => {
    if (professionalId) {
      fetchProfileData(professionalId);
    }
  }, [professionalId, fetchProfileData]);

  /**
   * Effet pour traiter les données récupérées
   */
  useEffect(() => {
    if (currentProfessional) {
      const filteredData = filterSensitiveData(currentProfessional);
      setProfileData(filteredData);
    }
  }, [currentProfessional, filterSensitiveData]);

  /**
   * Fonction utilitaire pour vérifier si le professionnel accepte de nouveaux patients
   */
  const acceptsNewPatients = profileData?.nouveau_patient_acceptes ?? false;

  /**
   * Fonction utilitaire pour obtenir la spécialité principale
   */
  const primarySpecialty = profileData?.specialite?.[0]?.nom_specialite || "Spécialité non renseignée";

  /**
   * Fonction utilitaire pour obtenir l'adresse complète
   */
  const fullAddress = profileData ? [
    profileData.adresse,
    profileData.commune,
    profileData.district,
    profileData.region
  ].filter(Boolean).join(", ") : "";

  return {
    // Données principales
    profileData,
    
    // États de chargement et d'erreur
    loading,
    error,
    
    // Fonctions utilitaires
    refetchProfile: () => professionalId && fetchProfileData(professionalId),
    acceptsNewPatients,
    primarySpecialty,
    fullAddress,
    
    // Données dérivées pour l'affichage
    hasCredentials: Boolean(
      profileData?.diplomes?.length || 
      profileData?.experiences?.length || 
      profileData?.publications?.length
    ),
    hasLanguages: Boolean(profileData?.langues?.length),
    hasKeywords: Boolean(profileData?.motCles?.length),
  };
};
