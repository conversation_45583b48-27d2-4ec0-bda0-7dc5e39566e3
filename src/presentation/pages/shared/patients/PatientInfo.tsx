import { Box } from "@mui/material";
import { useEffect, useState } from "react";
import CarnetDeSante from "@/presentation/pages/professional/patients/CarnetDeSante";
import HealthRecord from "@/presentation/components/features/patient/PatientCard/HealthRecord";
import ConsultationMedicale from "@/presentation/pages/professional/patients/ConsultationMedicale";
import SigneVitaux from "@/presentation/pages/professional/patients/SigneVitaux";
import { active_tab_enum } from "@/domain/models/enums";
import ProfilePatient from "./ProfilePatient";
import { useRegisterPatientState } from "@/presentation/hooks/useRegisterPatientState";
import Facturation from "@/presentation/pages/professional/patients/Facturation";
import { useCarnetDeSanteState } from "@/presentation/hooks/useCarnetDeSanteState";
import useSearchProfessional from "@/presentation/hooks/use-search-professional";
import { useAppSelector } from "@/presentation/hooks/redux";

const RenderPatientInfo = (activeTab: active_tab_enum) => {
  switch (activeTab) {
    case active_tab_enum.carnetDeSante:
      return <CarnetDeSante />;
    case active_tab_enum.consultationMedicale:
      return <ConsultationMedicale />;
    case active_tab_enum.signeVitaux:
      return <SigneVitaux />;
    case active_tab_enum.facturation:
      return <Facturation />;
    default:
      return <p>Aucune rendue disponible</p>;
  }
};

const PatientInfo = () => {
  const { searchProfessionalById } = useSearchProfessional();
  const { activeTab, isProfile, setIsProfile } = useCarnetDeSanteState();
  const { resetState } = useRegisterPatientState();
  const handleBack = () => {
    setIsProfile(false);
    resetState();
  };

  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.id
  );

  useEffect(() => {
    if (professionalId) {
      searchProfessionalById({ id: professionalId });
    }
  }, [professionalId]);

  return (
    <Box className="w-full">
      <HealthRecord />
      {isProfile ? (
        <ProfilePatient handleBack={handleBack} />
      ) : (
        RenderPatientInfo(activeTab)
      )}
    </Box>
  );
};

export default PatientInfo;
