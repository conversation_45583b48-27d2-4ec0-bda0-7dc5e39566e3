import AutoWrite from "@/presentation/components/features/home/<USER>/AutoWrite";
import Features from "@/presentation/components/features/home/<USER>/Features";
import SearchBar from "@/presentation/components/common/searchBar/SearchBar";
import Solution from "@/presentation/components/features/home/<USER>/Solution";
import FAQ from "@/presentation/components/features/home/<USER>/FAQ";
import Partenaires from "./component/Partenaires";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";

const Home = () => {
  return (
    <div className="flex flex-col">
      {/* Hero Section avec un design premium */}
      <div className="relative overflow-hidden bg-gradient-to-br from-meddoc-fonce via-meddoc-fonce/90 to-meddoc-fonce min-h-[80vh] flex flex-col justify-center">
        {/* Éléments décoratifs avancés */}
        <div className="absolute inset-0 z-0">
          {/* <PERSON><PERSON><PERSON> de fond */}
          <div className="absolute inset-0 opacity-5 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJ3aGl0ZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSIyIiBjeT0iMiIgcj0iMiIvPjwvZz48L3N2Zz4=')]"></div>

          {/* Cercles lumineux */}
          <div className="absolute top-[-10%] left-[-5%] w-[40%] h-[40%] rounded-full bg-meddoc-primary/30 blur-[120px] animate-pulse-slow"></div>
          <div className="absolute bottom-[-15%] right-[-10%] w-[50%] h-[50%] rounded-full bg-meddoc-secondary/20 blur-[150px] animate-pulse-slow animation-delay-2000"></div>

          {/* Lignes décoratives */}
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-meddoc-primary/50 to-transparent"></div>
          <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-meddoc-secondary/50 to-transparent"></div>
        </div>

        {/* Contenu principal avec structure en deux parties */}
        <div className="container mx-auto px-4 py-16 relative z-10">
          {/* Première partie: Titre et badge */}
          <div className="mb-16">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, ease: "easeOut" }}
              className="text-center max-w-5xl mx-auto"
            >
              {/* Badge premium */}
              {/* <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="inline-flex items-center px-4 py-1.5 rounded-full bg-gradient-to-r from-meddoc-primary/20 to-meddoc-secondary/20 backdrop-blur-sm border border-white/10 mb-6 mx-auto"
              >
                <div className="w-2 h-2 rounded-full bg-meddoc-primary animate-pulse mr-2"></div>
                <span className="text-white/90 text-sm font-medium">Plateforme de santé numérique</span>
              </motion.div> */}

              {/* Titre principal avec animation améliorée et hauteur fixe */}
              <motion.h1
                initial={{ opacity: 0, y: 40 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1, delay: 0.4, ease: "easeOut" }}
                className="text-3xl md:text-6xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-white to-white/80 leading-tight mb-6"
              >
                <div className="min-h-[50px] sm:min-h-[100px] md:min-h-[160px] md:min-h-[160px] flex flex-col justify-center"> {/* Hauteur minimale fixe */}
                  Prenez rendez-vous en ligne <br className="hidden md:block" />
                  aujourd'hui chez votre <span className="relative inline-block">
                    <span className="relative z-10"><AutoWrite /></span>

                  </span>
                </div>
              </motion.h1>
            </motion.div>
          </div>

          {/* Deuxième partie: Barre de recherche (fixe et indépendante du titre) */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.9 }}
            className="relative max-w-4xl mx-auto"
          >
            {/* Effet de lumière derrière la barre de recherche */}
            <div className="absolute -inset-1 bg-gradient-to-r from-meddoc-primary/50 via-meddoc-secondary/50 to-meddoc-primary/50 rounded-xl blur-xl opacity-70 group-hover:opacity-100 transition duration-1000 animate-gradient-x"></div>

            <div className="relative z-999">
              <SearchBar className="w-full mx-auto flex justify-center rounded-xl shadow-[0_8px_30px_rgba(0,0,0,0.3)] backdrop-blur-sm bg-white/5 border border-white/10 p-1 z-999" />
            </div>

            {/* Indicateurs de confiance */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 1, delay: 1.2 }}
              className="flex flex-wrap justify-center gap-x-8 gap-y-2 mt-6 text-white/60 text-sm"
            >
              {/* <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-meddoc-primary" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span>+10,000 professionnels</span>
              </div> */}
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-meddoc-primary" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span>Service 100% gratuit</span>
              </div>
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-meddoc-primary" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span>Disponible 24h/24 et 7j/7</span>
              </div>
            </motion.div>
          </motion.div>
        </div>


        <Features />
      </div>

      <Solution />
      <Partenaires />
      {/* Section "Des questions?" avec design amélioré et animations */}
      <motion.div
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="relative py-16 sm:py-24 overflow-hidden"
      >
        {/* Arrière-plan avec dégradé */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-50 to-slate-100 z-0"></div>

        {/* Éléments décoratifs */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 1, delay: 0.2 }}
          className="absolute top-10 right-10 w-32 h-32 rounded-full bg-meddoc-primary/5 z-0"
        ></motion.div>
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 1, delay: 0.4 }}
          className="absolute bottom-10 left-10 w-40 h-40 rounded-full bg-meddoc-secondary/5 z-0"
        ></motion.div>

        {/* Contenu principal */}
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-4xl mx-auto bg-white rounded-2xl shadow-xl overflow-hidden">
            <div className="flex flex-col md:flex-row">
              {/* Partie gauche avec image/icône */}
              <div className="bg-gradient-to-br from-meddoc-fonce to-meddoc-primary md:w-1/3 p-8 flex items-center justify-center">
                <motion.div
                  initial={{ scale: 0.8, rotate: -10 }}
                  whileInView={{ scale: 1, rotate: 0 }}
                  viewport={{ once: true }}
                  transition={{
                    type: "spring",
                    stiffness: 100,
                    delay: 0.3
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-24 w-24 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </motion.div>
              </div>

              {/* Partie droite avec texte et bouton */}
              <div className="md:w-2/3 p-8 md:p-10">
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  whileInView={{ y: 0, opacity: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5 }}
                >
                  <h2 className="text-3xl font-bold text-meddoc-fonce sm:text-4xl mb-4">Des questions ?</h2>
                </motion.div>

                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  whileInView={{ y: 0, opacity: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                >
                  <p className="text-gray-600 mb-6">
                    Consultez notre Centre d'aide pour trouver des réponses à vos questions.
                  </p>
                </motion.div>

                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  whileInView={{ y: 0, opacity: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  className="flex flex-col sm:flex-row gap-4"
                >
                  <Link to="/assistance">
                    <motion.button
                      whileHover={{ scale: 1.05, boxShadow: "0 10px 25px -5px rgba(39, 170, 225, 0.3)" }}
                      whileTap={{ scale: 0.95 }}
                      className="bg-gradient-to-r from-meddoc-primary to-meddoc-secondary text-white font-medium px-6 py-3 rounded-lg shadow-md flex items-center justify-center"
                    >
                      <span className="mr-2">OUVRIR LE CENTRE D'AIDE</span>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                    </motion.button>
                  </Link>

                 
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

    </div>
  );
};

export default Home;
