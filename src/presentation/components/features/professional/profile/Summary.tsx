import React, { useState } from "react";
import { ProfessionalCardDTO } from "@/domain/DTOS";
import { CheckIcon, MapPin, XIcon, Phone, Mail } from "lucide-react";
import { motion } from "framer-motion";
import AvailabilityModal from "@/presentation/components/common/Modal/AvailabilityModal";
import { ExtendedProfessionalProfile } from "@/presentation/hooks/useProfileData";

interface SummaryProps {
  professional: ExtendedProfessionalProfile | null;
  stickyResumeRef: React.RefObject<HTMLDivElement>;
}

export const Summary: React.FC<SummaryProps> = ({
  professional,
  stickyResumeRef,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const handleModalStateChange = async (isOpen: boolean) => {
    setIsModalOpen(isOpen);
  };

  return (
    <div
      className="bg-white p-6 rounded-lg shadow-md border border-gray-200 sticky top-[155px]"
      ref={stickyResumeRef}
    >
      <h2 className="text-xl font-bold text-meddoc-fonce mb-4">En résumé</h2>
      <div className="space-y-4">
        <div className="flex items-center text-gray-600">
          <span className="mr-2">
            {professional?.nouveau_patient_acceptes ? (
              <CheckIcon className="h-4 w-4 text-green-500" />
            ) : (
              <XIcon className="h-4 w-4 text-red-500" />
            )}
          </span>
          {professional?.nouveau_patient_acceptes
            ? "Accepte les nouveaux patients sur MEDDoC"
            : "N'accepte plus les nouveaux patients sur MEDDoC"}
        </div>
        <div className="flex items-center text-gray-600">
          <span className="mr-2">
            <MapPin className="h-4 w-4 text-meddoc-fonce" />
          </span>
          <p>
            {professional?.etablissements_professionnel[0].nom_etablissement} -{" "}
            {professional?.etablissements_professionnel[0].equipe}
          </p>
        </div>
        <p className="text-gray-600">{professional?.adresse}</p>
        <button
          className="w-full bg-gradient-to-r from-meddoc-primary to-meddoc-secondary text-white py-3 rounded-lg font-medium"
          onClick={() => handleModalStateChange(true)}
        >
          Prendre rendez-vous
        </button>

        <AvailabilityModal
          isOpen={isModalOpen}
          onClose={() => handleModalStateChange(false)}
          professionalInformations={professional}
        />
      </div>
    </div>
  );
};
