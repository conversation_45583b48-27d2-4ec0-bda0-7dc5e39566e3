import React, { memo } from "react";
import { ProfessionalCardDTO } from "@/domain/DTOS";
import { InfoIcon, Award, Briefcase, FileText, Languages } from "lucide-react";
import { motion } from "framer-motion";
import { ExtendedProfessionalProfile } from "@/presentation/hooks/useProfileData";

interface ExpertiseSectionProps {
  professional: ExtendedProfessionalProfile | null;
}

const ExpertiseSectionComponent: React.FC<ExpertiseSectionProps> = ({
  professional,
}) => {
  return (
    <motion.section
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      id="essentiel"
      className="bg-white p-6 rounded-2xl shadow-xl border border-gray-100 overflow-hidden relative"
    >
      {/* Effet de fond décoratif */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-meddoc-primary/5 to-meddoc-secondary/5 rounded-full -translate-y-16 translate-x-16"></div>

      <div className="flex items-start gap-3 relative z-10">
        <div className="bg-gradient-to-br from-meddoc-primary to-meddoc-secondary p-2 rounded-lg">
          <InfoIcon className="h-5 w-5 text-white" />
        </div>
        <div className="flex-1">
          <h2 className="text-2xl font-bold mb-6 text-meddoc-fonce">
            Expertises et actes
          </h2>

          {/* Spécialités principales */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center gap-2">
              <Award className="h-4 w-4 text-meddoc-primary" />
              Spécialités
            </h3>
            <div className="flex flex-wrap gap-3">
              {professional?.specialite?.map((expertise, index) => (
                <motion.span
                  key={expertise.nom_specialite}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  className="bg-gradient-to-r from-meddoc-primary/10 to-meddoc-secondary/10 text-meddoc-fonce px-4 py-2 rounded-full text-sm font-medium border border-meddoc-primary/20 hover:shadow-md transition-shadow"
                >
                  {expertise.nom_specialite}
                </motion.span>
              ))}
            </div>
          </div>

          {/* Types de consultation */}
          {professional?.types_consultation && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center gap-2">
                <Briefcase className="h-4 w-4 text-meddoc-primary" />
                Types de consultation
              </h3>
              <div className="flex flex-wrap gap-2">
                {Array.isArray(professional.types_consultation) ? (
                  professional.types_consultation.map((type, index) => (
                    <span
                      key={index}
                      className="bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm border border-blue-200"
                    >
                      {type}
                    </span>
                  ))
                ) : (
                  <span className="bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm border border-blue-200">
                    {professional.types_consultation}
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Mots-clés professionnels */}
          {professional?.motCles && professional.motCles.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center gap-2">
                <FileText className="h-4 w-4 text-meddoc-primary" />
                Domaines d'expertise
              </h3>
              <div className="flex flex-wrap gap-2">
                {professional.motCles.map((motCle, index) => (
                  <motion.span
                    key={motCle.id || index}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm hover:bg-gray-200 transition-colors"
                  >
                    {motCle.mot_cle}
                  </motion.span>
                ))}
              </div>
            </div>
          )}

          {/* Langues parlées */}
          {professional?.langues && professional.langues.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center gap-2">
                <Languages className="h-4 w-4 text-meddoc-primary" />
                Langues parlées
              </h3>
              <div className="flex flex-wrap gap-2">
                {professional.langues.map((langue, index) => (
                  <motion.span
                    key={langue.id || index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm border border-green-200"
                  >
                    {langue.nom_langue} {langue.niveau && `(${langue.niveau})`}
                  </motion.span>
                ))}
              </div>
            </div>
          )}

          {/* Message si aucune expertise spécifique */}
          {(!professional?.specialite ||
            professional.specialite.length === 0) && (
            <div className="text-center py-8 text-gray-500">
              <InfoIcon className="h-12 w-12 mx-auto mb-3 text-gray-300" />
              <p>Aucune expertise spécifique renseignée</p>
            </div>
          )}
        </div>
      </div>
    </motion.section>
  );
};

// Memoize the component to prevent unnecessary re-renders
export const ExpertiseSection = memo(ExpertiseSectionComponent);
