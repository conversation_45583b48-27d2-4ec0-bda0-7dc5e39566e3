import React, { memo } from "react";
import { ProfessionalCardDTO } from "@/domain/DTOS";
import LeafletMap from "@/presentation/components/features/leaflet/LeafletMap";
import { MapPin, Navigation, Info } from "lucide-react";
import { motion } from "framer-motion";
import { ExtendedProfessionalProfile } from "@/presentation/hooks/useProfileData";

interface MapSectionProps {
  professional: ExtendedProfessionalProfile | null;
}

const MapSectionComponent: React.FC<MapSectionProps> = ({ professional }) => {
  return (
    <motion.section
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.1 }}
      id="carte"
      className="bg-white p-6 rounded-2xl shadow-xl border border-gray-100 overflow-hidden relative"
    >
      {/* Effet de fond décoratif */}
      <div className="absolute bottom-0 right-0 w-36 h-36 bg-gradient-to-br from-meddoc-primary/5 to-meddoc-secondary/5 rounded-full translate-y-18 translate-x-18"></div>

      <div className="flex items-start gap-3 relative z-10">
        <div className="bg-gradient-to-br from-meddoc-primary to-meddoc-secondary p-2 rounded-lg">
          <MapPin className="h-5 w-5 text-white" />
        </div>
        <div className="flex-1">
          <h2 className="text-2xl font-bold mb-6 text-meddoc-fonce">
            Localisation et accès
          </h2>

          {/* Informations de l'établissement */}
          {professional?.etablissements_professionnel?.[0] && (
            <motion.div
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100"
            >
              <h3 className="font-semibold text-gray-800 mb-2 flex items-center gap-2">
                <Navigation className="h-4 w-4 text-meddoc-primary" />
                {professional.etablissements_professionnel[0].nom_etablissement}
              </h3>
              {professional.etablissements_professionnel[0].equipe && (
                <p className="text-meddoc-primary font-medium mb-2">
                  {professional.etablissements_professionnel[0].equipe}
                </p>
              )}
              <p className="text-gray-600 mb-3 flex items-start gap-2">
                <MapPin className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                {professional.adresse}
              </p>

              {/* Informations d'accès supplémentaires */}
              {professional.informations_acces && (
                <div className="mt-3 p-3 bg-white/70 rounded-lg border border-blue-200">
                  <h4 className="font-medium text-gray-800 mb-1 flex items-center gap-2">
                    <Info className="h-3 w-3 text-meddoc-primary" />
                    Informations d'accès
                  </h4>
                  <p className="text-sm text-gray-600">
                    {professional.informations_acces}
                  </p>
                </div>
              )}
            </motion.div>
          )}

          {/* Carte interactive */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="h-64 mt-4 rounded-xl overflow-hidden shadow-lg border border-gray-200"
          >
            {professional && (
              <LeafletMap filteredData={[professional]} hoveredId={null} />
            )}
          </motion.div>

          {/* Informations de contact publiques (si disponibles) */}
          {professional?.contacts && professional.contacts.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="mt-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-100"
            >
              <h4 className="font-medium text-gray-800 mb-2">
                Informations de contact
              </h4>
              <div className="text-sm text-gray-600">
                {/* Afficher uniquement les informations publiques de contact */}
                <p>Contactez ce professionnel via la plateforme MEDDoC</p>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </motion.section>
  );
};

// Memoize the component to prevent unnecessary re-renders
export const MapSection = memo(MapSectionComponent);
