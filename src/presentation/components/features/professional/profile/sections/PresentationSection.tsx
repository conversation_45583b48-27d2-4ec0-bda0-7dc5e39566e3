import React, { memo } from "react";
import { ProfessionalCardDTO } from "@/domain/DTOS";
import { ScrollText, CreditCard, Shield } from "lucide-react";
import { motion } from "framer-motion";
import { ExtendedProfessionalProfile } from "@/presentation/hooks/useProfileData";

interface PresentationSectionProps {
  professional: ExtendedProfessionalProfile | null;
}

const PresentationSectionComponent: React.FC<PresentationSectionProps> = ({
  professional,
}) => {
  return (
    <motion.section
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.3 }}
      id="presentation"
      className="bg-white p-6 rounded-2xl shadow-xl border border-gray-100 overflow-hidden relative"
    >
      {/* Effet de fond décoratif */}
      <div className="absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-meddoc-secondary/5 to-meddoc-primary/5 rounded-full -translate-y-16 -translate-x-16"></div>

      <div className="flex items-start gap-3 relative z-10">
        <div className="bg-gradient-to-br from-meddoc-secondary to-meddoc-primary p-2 rounded-lg">
          <ScrollText className="h-5 w-5 text-white" />
        </div>
        <div className="flex-1">
          <h2 className="text-2xl font-bold mb-6 text-meddoc-fonce">
            Présentation
          </h2>

          {/* Présentation générale */}
          {professional?.presentation_generale && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mb-6 p-4 bg-gradient-to-r from-slate-50 to-gray-50 rounded-lg border border-gray-100"
            >
              <p className="text-gray-700 leading-relaxed">
                {professional.presentation_generale}
              </p>
            </motion.div>
          )}

          {/* Modes de paiement acceptés */}
          {professional?.modes_paiement_acceptes &&
            professional.modes_paiement_acceptes.length > 0 && (
              <motion.div
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="mb-6"
              >
                <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center gap-2">
                  <CreditCard className="h-4 w-4 text-meddoc-primary" />
                  Modes de paiement acceptés
                </h3>
                <div className="flex flex-wrap gap-2">
                  {professional.modes_paiement_acceptes.map((mode, index) => (
                    <motion.span
                      key={index}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className="bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm border border-green-200"
                    >
                      {mode}
                    </motion.span>
                  ))}
                </div>
              </motion.div>
            )}

          {/* Informations sur l'acceptation de nouveaux patients */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100"
          >
            <h3 className="text-lg font-semibold text-gray-800 mb-2 flex items-center gap-2">
              <Shield className="h-4 w-4 text-meddoc-primary" />
              Disponibilité
            </h3>
            <div className="flex items-center gap-2">
              <div
                className={`w-3 h-3 rounded-full ${professional?.nouveau_patient_acceptes ? "bg-green-500" : "bg-red-500"}`}
              ></div>
              <span className="text-gray-700">
                {professional?.nouveau_patient_acceptes
                  ? "Accepte de nouveaux patients"
                  : "N'accepte plus de nouveaux patients pour le moment"}
              </span>
            </div>
          </motion.div>

          {/* Raison sociale (si différente du nom) */}
          {professional?.raison_sociale && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              className="p-3 bg-gray-50 rounded-lg border border-gray-200"
            >
              <h4 className="font-medium text-gray-800 mb-1">Raison sociale</h4>
              <p className="text-sm text-gray-600">
                {professional.raison_sociale}
              </p>
            </motion.div>
          )}

          {/* Message si aucune présentation */}
          {!professional?.presentation_generale && (
            <div className="text-center py-8 text-gray-500">
              <ScrollText className="h-12 w-12 mx-auto mb-3 text-gray-300" />
              <p>Aucune présentation disponible</p>
            </div>
          )}
        </div>
      </div>
    </motion.section>
  );
};

// Memoize the component to prevent unnecessary re-renders
export const PresentationSection = memo(PresentationSectionComponent);
