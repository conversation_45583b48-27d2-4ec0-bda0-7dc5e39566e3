import DateClass from "./DateClass";

export const generateSchedule = (
  start: DateClass,
  dailyTimes: { [key: string]: string[] },
  dateCount: number,
) => {
  let temp = start;
  const days = [start];
  for (let i = 0; i < dateCount; i++) {
    temp = temp.lendemain();
    days.push(temp);
  }
  return days.map((date) => {
    const dayName = date.toLocaleDateString("fr-FR", { weekday: "long" });
    const monthName = date.toLocaleDateString("fr-FR", { month: "short" });
    return {
      dayName: dayName,
      day: `${date.getJour()} ${monthName}`,
      times: dailyTimes[dayName] || [],
    };
  });
};
