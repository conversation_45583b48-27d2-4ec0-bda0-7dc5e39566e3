export default function getDateFromDay(
    startDate: string,
    targetDay: string,
): string {
    const jours = ["Dim.", "Lun.", "Mar.", "Mer.", "Jeu.", "Ven.", "Sam."];

    // Convertir startDate en objet Date
    const date = new Date(startDate);
    const startDayIndex = date.getDay(); // 0 = Dim., 1 = Lun., etc.
    const targetDayIndex = jours.indexOf(targetDay);

    if (targetDayIndex === -1) {
        throw new Error("Jour invalide");
    }

    // Calculer la différence en jours
    let diff = targetDayIndex - startDayIndex;
    if (diff < 0) diff += 7; // Si le jour est passé, aller à la semaine suivante

    // Ajouter la différence en jours
    date.setDate(date.getDate() + diff);

    return date.toISOString(); // Retourner la date en format ISO
}
