// Validation
export const validateField = (
  name: string,
  value: string | Date | null | number,
  errors: { [key: string]: string },
  onValidation: (errors: { [key: string]: string }) => void,
) => {
  const newErrors = { ...errors };
  switch (name) {
    case "duration":
      {
        const durationValue = typeof value === "string"
          ? parseInt(value)
          : (typeof value === "number" ? value : 0);
        if (!value) {
          newErrors[name] = "La durée est requise";
        } else if (durationValue < 5) {
          newErrors[name] = "La durée minimum est de 5 minutes";
        } else {
          delete newErrors[name];
        }
      }
      break;
    case "maxDays": {
      const maxDaysValue = typeof value === "string"
        ? parseInt(value)
        : (typeof value === "number" ? value : 0);
      if (!value) {
        newErrors[name] = "La période maximale est requise";
      } else if (maxDaysValue < 1) {
        newErrors[name] = "La période doit être d'au moins 1 jour";
      } else {
        delete newErrors[name];
      }
      break;
    }
    case "minHours": {
      const minHoursValue = typeof value === "string"
        ? parseInt(value)
        : (typeof value === "number" ? value : 0);
      if (!value) {
        newErrors[name] = "La période minimale est requise";
      } else if (minHoursValue < 1) {
        newErrors[name] = "La période doit être d'au moins 1 heure";
      } else {
        delete newErrors[name];
      }
      break;
    }
    case "dureePause": {
      const dureePauseValue = typeof value === "string"
        ? parseInt(value)
        : (typeof value === "number" ? value : 0);
      if (!value) {
        newErrors[name] = "La durée est requise";
      } else {
        delete newErrors[name];
      }
      break;
    }
    case "breakDuration": {
      const breakDurationValue = typeof value === "string"
        ? parseInt(value)
        : (typeof value === "number" ? value : 0);
      if (!value) {
        newErrors[name] = "La durée de pause est requise";
      } else if (breakDurationValue < 1 || breakDurationValue > 240) {
        newErrors[name] = "Doit être comprise entre 1 minute et 4 heures";
      } else {
        delete newErrors[name];
      }
      break;
    }
    case "startDate":
      if (!value) {
        newErrors[name] = "La date de début est requise";
      } else if (value instanceof Date) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const startDate = new Date(value);
        startDate.setHours(0, 0, 0, 0);

        if (startDate < today) {
          newErrors[name] =
            "La date de début doit être supérieure ou égale à aujourd'hui";
        } else {
          delete newErrors[name];
        }
      }
      break;
    case "endDate":
      if (!value) {
        newErrors[name] = "La date de fin est requise";
      } else if (value instanceof Date) {
        const startDateValue = errors["startDate"] ? null : value;
        if (startDateValue && value < startDateValue) {
          newErrors[name] =
            "La date de fin doit être supérieure à la date de début";
        } else {
          delete newErrors[name];
        }
      }
      break;
    case "maxReservationsPerDay": {
      const maxReservationsPerDayValue = typeof value === "string"
        ? parseInt(value)
        : (typeof value === "number" ? value : 0);
      if (!value) {
        newErrors[name] = "Le nombre maximum de réservations est requis";
      } else if (
        maxReservationsPerDayValue < 1 || maxReservationsPerDayValue > 96
      ) {
        newErrors[name] = "Doit être comprise entre 1 et 96";
      } else {
        delete newErrors[name];
      }
      break;
    }
  }

  onValidation(newErrors);
};
